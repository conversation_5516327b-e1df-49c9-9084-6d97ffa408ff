"""
超星学习通考试入口验证码处理模块
专门用于处理考试入口页面的滑块验证码
基于CaptchaHandler.py，针对考试入口进行优化
"""
import requests
import time
import re
import hashlib
import random
import ddddocr
import urllib3
from loguru import logger
from CaptchaHandler import CaptchaHandler

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ExamCaptchaHandler(CaptchaHandler):
    """超星学习通考试入口验证码处理类"""
    
    def __init__(self, session=None, exam_url=None):
        """
        初始化考试验证码处理器
        
        Args:
            session: requests.Session对象，如果为None则创建新的会话
            exam_url: 考试入口URL，用于设置referer和获取captchaId
        """
        # 考试入口的固定captchaId（从考试入口相关文件中获取）
        exam_captcha_id = 'Ew0z9skxsLzVKQjmeObQiRVLxkxbPkRF'
        
        # 设置考试入口的referer
        exam_referer = exam_url if exam_url else 'https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes'
        
        # 调用父类初始化，传入考试专用参数
        super().__init__(session=session, captcha_id=exam_captcha_id, referer=exam_referer)
        
        self.exam_url = exam_url
        logger.info(f"考试验证码处理器初始化完成")
        logger.info(f"使用captchaId: {self.captcha_id}")
        logger.info(f"使用referer: {self.referer}")
    
    def detect_captcha_id_from_page(self, page_content):
        """
        从考试页面内容中检测captchaId
        
        Args:
            page_content: 页面HTML内容
            
        Returns:
            str: 检测到的captchaId，如果未找到则返回默认值
        """
        try:
            # 尝试从页面中提取captchaId
            patterns = [
                r'captchaId["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'captcha[_-]?id["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'id\s*:\s*["\']([A-Za-z0-9]{32})["\']'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_content, re.IGNORECASE)
                if matches:
                    captcha_id = matches[0]
                    logger.info(f"从页面检测到captchaId: {captcha_id}")
                    return captcha_id
            
            logger.warning("未能从页面检测到captchaId，使用默认值")
            return self.captcha_id
            
        except Exception as e:
            logger.error(f"检测captchaId时出错: {e}")
            return self.captcha_id
    
    def update_captcha_id(self, new_captcha_id):
        """
        更新captchaId
        
        Args:
            new_captcha_id: 新的captchaId
        """
        if new_captcha_id and new_captcha_id != self.captcha_id:
            logger.info(f"更新captchaId: {self.captcha_id} -> {new_captcha_id}")
            self.captcha_id = new_captcha_id
    
    def solve_exam_captcha(self, max_attempts=3):
        """
        解决考试入口的滑块验证码
        
        Args:
            max_attempts: 最大尝试次数
            
        Returns:
            tuple: (验证成功状态, validate字符串) - (bool, str)
        """
        logger.info("开始处理考试入口滑块验证码...")
        
        for attempt in range(1, max_attempts + 1):
            try:
                logger.info(f"第{attempt}次尝试解决考试验证码...")
                
                # 获取验证码图片
                shade_image, cutout_image, token = self.get_captcha_images()
                if shade_image is None or cutout_image is None or token is None:
                    logger.error("获取考试验证码图片失败")
                    continue
                
                # 识别滑动距离
                x_distance = self.recognize_slide_distance(shade_image, cutout_image)
                if x_distance is None:
                    logger.error("识别考试验证码滑动距离失败")
                    continue
                
                # 验证验证码
                success, validate_str = self.verify_captcha(token, x_distance)
                if success:
                    logger.success("考试入口滑块验证码处理成功！")
                    return True, validate_str
                
                # 验证失败，等待一段时间再重试
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"解决考试验证码异常: {str(e)}")
                time.sleep(2)
        
        logger.error(f"考试验证码解决失败，已尝试{max_attempts}次")
        return False, None

# 测试代码
if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add("exam_captcha.log", rotation="10 MB", level="INFO")
    logger.add(lambda msg: print(msg), level="INFO")
    
    # 测试考试验证码处理
    exam_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?reset=true&examId=7689933&courseId=253891757&classId=124398558&cpi=404722142"
    exam_captcha_handler = ExamCaptchaHandler(exam_url=exam_url)
    success, validate_str = exam_captcha_handler.solve_exam_captcha()
    print(f"考试验证码处理结果: {'成功' if success else '失败'}")
    if success and validate_str:
        print(f"获取到validate字符串: {validate_str}")
