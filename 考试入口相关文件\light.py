import requests

url = "https://captcha.chaoxing.com/light.js?t=29228623"

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'sec-ch-ua-platform': "\"Windows\"",
  'sec-ch-ua': "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
  'sec-ch-ua-mobile': "?0",
  'Sec-Fetch-Site': "same-site",
  'Sec-Fetch-Mode': "no-cors",
  'Sec-Fetch-Dest': "script",
  'Referer': "https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?reset=true&examId=7689933&courseId=253891757&classId=124398558&cpi=404722142",
  'Accept-Language': "zh-CN,zh;q=0.9",
  'Cookie': "route=c17caf14c9dd9ac7be8390c41e5ffc18; fid=75096; source=\"\"; _uid=340940394; _d=1753642894627; UID=340940394; vc3=RsPdqSHWU9O%2BU9sARdDmfOAvrcYjoloIkYDTKd9%2BhYh0UlskUzsbwqg5vRN7035W7%2BRfc%2BXwse4j1gI05g4icHCnGJ%2B5wWxPurH9lLZq6DbhjErGATQzhW%2FiTxHTaKBDCpGxkt9bLbri%2Fcwiy6ozZHzSIW0YyzVbSCaSl5YlqLY%3Ddd8a4f804e9b96e095790941da405630; uf=dff23984ef72c20bf7dc7b9c050347e5c5e77f9555732ddbbf4da346db721aac27910e919d98e74ca6f2b7a864c73a8c190a36865f1aaba0c49d67c0c30ca5043ad701c8b4cc548c0234d89f51c3dccf1364cde7cb9fe997b919cf4f30ba0bf0c589688fe9065f4968ee6672475c7fe46a779ea7e274c9b6822d20be5392985671b2538c618d4d32dd7f5f2554301260d75da20f47b1f73400726a3426cc7f6c2e1dbe49ec00162ab989eeb63fa90b5538d828f3cfaf329a3ab585abb356a0d4; cx_p_token=0399d18f149e97a63dba5b170ea99f09; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIzNDA5NDAzOTQiLCJsb2dpblRpbWUiOjE3NTM2NDI4OTQ2MjgsImV4cCI6MTc1NDI0NzY5NH0.Fjz_xFI7ui0_sDrY9U2gRBYBCH6rjb5a9SiPhjpM38s; xxtenc=b7b8f81b6cdd85584fe09b26d43f883c; DSSTASH_LOG=C_38-UN_4927-US_340940394-T_1753642894628; spaceFid=75096; spaceRoleId=\"\"; tl=1"
}

response = requests.get(url, headers=headers)

print(response.text)