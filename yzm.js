const CryptoJS = require('crypto-js');
// 随机生成一段数字代码
function _0x11dbad() {
    // var _0x5c52a4 = _0x2e675c;
    for (var _0x55977e = [], _0x12474f = "0123456789abcdef", _0x33c6e8 = 0x0; _0x33c6e8 < 0x24; _0x33c6e8++)
        _0x55977e[_0x33c6e8] = _0x12474f['substr'](Math['floor'](0x10 * Math["random"]()), 0x1);
    return _0x55977e[0xe] = '4',
        _0x55977e[0x13] = _0x12474f['substr'](0x3 & _0x55977e[0x13] | 0x8, 0x1),
        _0x55977e[0x8] = _0x55977e[0xd] = _0x55977e[0x12] = _0x55977e[0x17] = '-',
        _0x55977e['join']('');
}
function GetSign(_0x4e0309) {
    _0x3fedba = 'qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAv'
    // 生成 captchaKey 的代码  _0x4e0309 (时间戳)
    _0x422ded = CryptoJS.MD5(_0x4e0309 + _0x11dbad()).toString()
    // 生成 token 的代码
    _0x4e0309 = CryptoJS.MD5(_0x4e0309 + _0x3fedba + 'slide' + _0x422ded).toString() + ':' + (parseInt(_0x4e0309) + 0x493e0) || ''
    captchaKey = _0x422ded
    token = _0x4e0309
    IV = CryptoJS.MD5(_0x3fedba + 'slide' + Date['now']() + _0x11dbad()).toString()
    return {
        'captchaKey': captchaKey,
        'token':token,
        'IV':IV,
    }
}
function f(NowTime){
    return CryptoJS.MD5('qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAv' + 'slide' + NowTime + _0x11dbad()).toString()
}