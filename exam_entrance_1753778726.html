<!DOCTYPE html>

<html lang="en">

<head>

    <meta charset="UTF-8">

    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

    <title>用户登录</title>

    <link href="https://passport2-static.chaoxing.com/css/fanya/common.css" rel="stylesheet">

    <link href="https://passport2-static.chaoxing.com/css/fanya/login.css?v=3" rel="stylesheet">

    <link href="https://passport2-static.chaoxing.com/css/fanya/twoFactorLoginPop.css" rel="stylesheet">

    <link href="https://passport2-static.chaoxing.com/css/fanya/pcXxt.css?v=2" rel="stylesheet">

    <script src="https://passport2-static.chaoxing.com/js/jquery.min.js" type="text/javascript"></script>

    <script src="https://passport2-static.chaoxing.com/js/common/jquery.base64.js" type="text/javascript"></script>

    <script src="https://passport2-static.chaoxing.com/js/fanya/util.js?v=1" type="text/javascript"></script>

    <script src="https://passport2-static.chaoxing.com/js/common/commonUtil.js?v=113" type="text/javascript"></script>

    <script src="https://passport2-static.chaoxing.com/js/common/crypto-js.min.js" type="text/javascript"></script>

    <script type="text/javascript" src="https://passport2-static.chaoxing.com/js/common/CXJSBridge2.js"></script>

    <script>

        $(function () {

            if ($("#shownotice").val() == 1) {

                showNotice($("#fid").val());

            }

            enterSubmit("loginBtn");



            if (top != self) {

                // $(".default-login").css("height","520px");

                //$(".default-login").css("border-radius","0px");

                //$(".default-login").css("box-shadow","");



                $("body").css("background", "none");

                $(".lg-container").css("background", "none");

                $(".lg-container").css("border-top", "none");





            }



            //只要配置了登录提示语，就都显示出来

            let loginTip = "";

            if (loginTip != "undefined" && loginTip.trim() != "") {

                $("#loginTip").show();

            }



            if ("https%3A%2F%2Fmooc1.chaoxing.com%2Fexam-ans%2Fexam%2Ftest%2Fexamcode%2Fexamnotes%3Freset%3Dtrue%26examId%3D221247981%26courseId%3D122555170%26classId%3D6991302%26cpi%3D356795813".indexOf("duxiu.com") != -1 || "https%3A%2F%2Fmooc1.chaoxing.com%2Fexam-ans%2Fexam%2Ftest%2Fexamcode%2Fexamnotes%3Freset%3Dtrue%26examId%3D221247981%26courseId%3D122555170%26classId%3D6991302%26cpi%3D356795813".indexOf("blyun.com") != -1 || "https%3A%2F%2Fmooc1.chaoxing.com%2Fexam-ans%2Fexam%2Ftest%2Fexamcode%2Fexamnotes%3Freset%3Dtrue%26examId%3D221247981%26courseId%3D122555170%26classId%3D6991302%26cpi%3D356795813".indexOf("zhizhen.com") != -1) {

                // $("#otherlogin").hide();

                $("#showlogintext").html("账号登录");

                $("#kf").hide();

            }

            if ($("#showthird").val() == "1") {

                $("#thirdLogin").show();

            }



            if (navigator.userAgent.indexOf("1000332") != -1) {

                $("#leftdiv").hide();

            }



            if ("https%3A%2F%2Fmooc1.chaoxing.com%2Fexam-ans%2Fexam%2Ftest%2Fexamcode%2Fexamnotes%3Freset%3Dtrue%26examId%3D221247981%26courseId%3D122555170%26classId%3D6991302%26cpi%3D356795813".indexOf("dayi100.com") != -1) {

                $("a[onclick='toRegister()']").hide();

            }



            if ("https%3A%2F%2Fmooc1.chaoxing.com%2Fexam-ans%2Fexam%2Ftest%2Fexamcode%2Fexamnotes%3Freset%3Dtrue%26examId%3D221247981%26courseId%3D122555170%26classId%3D6991302%26cpi%3D356795813".indexOf("mayuan.hebtu.edu.cn") > -1) {

                $('#fid').val(43350);

            }



            if ("https%3A%2F%2Fmooc1.chaoxing.com%2Fexam-ans%2Fexam%2Ftest%2Fexamcode%2Fexamnotes%3Freset%3Dtrue%26examId%3D221247981%26courseId%3D122555170%26classId%3D6991302%26cpi%3D356795813".indexOf("chatfile.chaoxing.com") != -1) {

                $("div.dz-logo").show();

                $("div.lg-container").addClass("lg-dz-bg");

                $("div.filingBox").hide();

                $(".lg-container .main").removeClass("heightCalc")

            }



            if ("https%3A%2F%2Fmooc1.chaoxing.com%2Fexam-ans%2Fexam%2Ftest%2Fexamcode%2Fexamnotes%3Freset%3Dtrue%26examId%3D221247981%26courseId%3D122555170%26classId%3D6991302%26cpi%3D356795813".indexOf("sslibrary.com") != -1) {

                $("div.lg-container").addClass("redBodybg");

                $("div.filingBox").css('border-top', 'none');

                //版权所有@世纪超星信息技术发展有限责任公司 京ICP备案号：10040544号-25京公网安备 11010802038946号

                $("div.filingBox p").html(

                        ' <p style="color: white;margin-bottom: 10px;"> 版权所有@世纪超星信息技术发展有限责任公司 </p>' +

                        '<p style="margin-bottom: 10px;"> <span class="icpSpan"><a class="col-blue" href="https://beian.miit.gov.cn" style="color: white;margin-right: 20px;">京ICP备案号：10040544号-25</a>' +

                        '<i></i><a  class="col-blue" style="color: white;" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010802038946">京公网安备 11010802038946号</a></span></p>'

                );

                // $("#otherlogin").hide();

                $(".lg-container .main").removeClass("heightCalc")

                $('#kf').hide();

            }



            var currentDomain = window.location.hostname;

            if (currentDomain == "passport2.hbqyg.cn") {

                $("#passportAgreement").hide();

            }

            if (currentDomain.indexOf("xuexi365.net") != -1) {

                document.title = "成都学习通笔记分享";

            }



            //根据参数控制是否显示其它登录

            if (getURLQuery('hideOtherLogin') && getURLQuery('hideOtherLogin') == '1') {

                $("#otherlogin").hide();

                $("#passportAgreement").css('bottom', '-46px');

            }



            //根据参数控制是否显示 密码眼睛

            if (getURLQuery('hidePasswordEye') && getURLQuery('hidePasswordEye') == '1') {

                $(".icon-eye").hide();

            }

        });

    </script>

</head>

<style type="text/css">

    .Agreement {

        text-align: center;

        font-size: 12px;

        color: #A8A8B3;

        position: relative;

        left: 0px;

        bottom: -40px;

    }



    .Agreement a {

        display: inline-block;

        padding: 0 5px;

        color: #3A8BFF

    }



    .lg-title img {

        display: block;

        width: 254px;

        height: 37px;

        margin: 0 auto;

    }



    .lg-dz-bg {

        background: url(/images/fanya/bg.png) no-repeat center;

        background-size: 100% 100%;

    }



    .dz-logo {

        width: 392px;

        height: 116px;

        margin: 0 auto;

        margin-bottom: 25px

    }



    .dz-logo img {

        display: inline-block;

        width: 100%;

        height: 100%;

    }

</style>

<script>var _CP_ = '';</script>

<script>

    retainlogin = 2;

    document.cookie = "retainlogin=" + (retainlogin == '3' ? '2' : retainlogin) + ";";

</script>

<body>

<div class="lg-container">

    <!--登录 start -->

    <div class="main  default-login-main ">

        <div class="dz-logo" hidden>

            <img src="/images/fanya/readlogo.png">

        </div>

        <!--默认登录 start-->

        <div class="default-login ">



                        <div class="lg-top fs14">

                <a href="/v5/toCustomer" target="_blank" class="service col-lightblue" id="kf">客服</a>

            </div>

            <div class="left" id="leftdiv" >

                                    <h3 class="lg-title" id="showlogintext" style="margin-bottom: 44px">

                                                                                    用户登录                                                                        </h3>

                                <p id="loginTip" style="display:none;padding-bottom:10px;color:#A8A8B3;font-size:12px"></p>



                                <form action="">

                    <input type="hidden" id="needVcode" name="needVcode" value=""/>

                    <input type="hidden" id="fid" name="fid" value="-1"/>

                    <input type="hidden" id="pid" name="pid" value="-1"/>

                    <input type="hidden" id="refer" name="refer" value="https%3A%2F%2Fmooc1.chaoxing.com%2Fexam-ans%2Fexam%2Ftest%2Fexamcode%2Fexamnotes%3Freset%3Dtrue%26examId%3D221247981%26courseId%3D122555170%26classId%3D6991302%26cpi%3D356795813"/>

                    <input type="hidden" id="forbidotherlogin" name="forbidotherlogin" value="0"/>

                    <input type="hidden" id="t" name="t" value="true"/>

                    <input type="hidden" id="_blank" value="0">

                    <input type="hidden" id="regurl" value="">

                    <input type="hidden" id="showthird" value="0">

                    <input type="hidden" id="retainlogin" value="2">

                    <input type="hidden" id="accounttip" value="">

                    <input type="hidden" id="pwdtip" value="">

                    <input type="hidden" id="quick" value="0">

                    <input type="hidden" id="showForgetPwd" value="3">

                    <input type="hidden" name="shownotice" id="shownotice" value="1"/>

                    <input type="hidden" id="hidecompletephone" name="hidecompletephone" value="0"/>

                    <input type="hidden" id="ebook" name="ebook" value="0"/>

                    <input type="hidden" id="allowSkip" name="allowSkip" value="0"/>

                    <input type="hidden" id="doubleFactorLogin" name="doubleFactorLogin" value="0"/>

                    <input type="hidden" id="independentId" name="independentId" value="0"/>

                    <input type="hidden" id="independentNameId" name="independentNameId" value="0"/>

                    <input type="hidden" id="otherLoginUrl" name="otherLoginUrl" value=""/>

                    <input type="hidden" id="topTip" name="topTip" value=""/>

                    <!--电话号码-->

                    <div class="lg-item icon-tel margin-btm24">

                        <input type="text" class="ipt-tel"

                               placeholder="手机号/超星号"

                                                           id="phone" maxlength="30">

                        <p class="err-txt" id="phoneMsg"></p>

                    </div>

                    <!--密码-->

                    <div class="lg-item item-pwd icon-pwd">

                        <input type="password" class="ipt-pwd"                                placeholder="学习通密码"  id="pwd" maxlength="20">

                        <i class="icon-eye icon-eye-close" onclick="initPassword(this)"></i>

                        <a href="javaScript:void(0)" onclick="toFindPwd()" class="col-lightblue pos-r24">忘记密码                            ?</a>

                        <p class="err-txt" id="pwdMsg"></p>

                    </div>

                    <input type="hidden" name="validate" id="validate"/>

                    <!--按钮不可点击 加上class：disable -->

                    <div class="btns-box">

                        <p class="err-tip" id="err-txt"></p>

                        <button type="button" class="btn-big-blue margin-btm24" onclick="loginByPhoneAndPwd();"

                                id="loginBtn">登录</button>

                    </div>

                </form>



                                                            <div class="clearaft fs14">

                            <div class="auto-lg-next col-blue fl"><span

                                    class="check-input "></span>下次自动登录                                <div class="tips">

                                    <span class="tipsIc"></span>

                                    <div class="tipsCon"><i></i>勾选后，登录状态保持7天；如不勾选则关闭浏览器即为退出</div>

                                </div>

                            </div>

                            <div class="lg-opr-right fr">

                                                                    <a href="javaScript:void(0)" onclick="toRegister()" class="col-blue fl">

                                                                                    新用户注册                                                                            </a>

                                                                            <a href="javaScript:void(0)" onclick="otherLogin(2)" class="col-blue fr">验证码登录</a>

                                                                                                </div>

                        </div>

                                                    <div class="line">

                                            <a href="javaScript:void(0)" onclick="otherLogin(3)" id="otherlogin"

                           class="col-lightblue fs14 icon-right">其它方式登录</a>

                                    </div>

                <p id="passportAgreement" class="Agreement" style="bottom: -25px">我已阅读并同意学习通<a

                        href="https://homewh.chaoxing.com/agree/privacyPolicy?appId=900001" target="_blank">《隐私政策》</a>和<a

                        href="https://homewh.chaoxing.com/agree/userAgreement?appId=900001" target="_blank">《用户协议》</a></p>

            </div>

                            <style>



    .social-login {

        color: #91A0B5;

        width: 100%;

        position: absolute;

        left: 0;

        bottom: 30px;

    }



    .social-login span {

        display: inline-block;

        margin: 0 10px;

    }



    .social-login span i {

        display: inline-block;

        width: 20px;

        height: 20px;

        margin-right: 6px;

    }



    .social-login span i img {

        width: 100%;

        height: 100%;

        vertical-align: middle;

    }



    .social-login span.marLeft30 {

        margin-left: 30px;

    }



    .ewmTab {

        width: 100%;

        height: 60px;

        margin-bottom: 14px;

    }



    .ewmTab li {

        display: inline-block;

        line-height: 60px;

        position: relative;

        padding: 0 23px;

        font-size: 16px;

        color: #474C59;

        cursor: pointer;

    }



    .ewmTab li span {

        display: inline-block;

        width: 32px;

        height: 4px;

        border-radius: 4px;

        background: #3A8BFF;

        position: absolute;

        left: 50%;

        bottom: 0;

        transform: translateX(-50%);

        display: none;

    }



    .ewmTab li.ewmCur {

        font-weight: 600;

        color: #131B26

    }



    .ewmTab li.ewmCur span {

        display: block

    }



    .wxtip {

        line-height: 20px;

        margin-top: 20px;

    }

</style>

<div class="right" id="rightdiv" >

        <div id="step1">

        <div class="ecode-box" style="position: relative;">

            <style>

                .ewmDisable {

                    width: 100%;

                    height: 100%;

                    position: absolute;

                    left: 0;

                    top: 0;

                    background: rgba(255, 255, 255, 0.9);

                    z-index: 3;

                    display: none;

                }



                .ewmDisable p {

                    width: 100%;

                    text-align: center;

                    font-size: 18px;

                    color: #181e33;

                    margin-top: 80px;

                }



                .ewmDisable a {

                    display: inline-block;

                    width: 94px;

                    height: 34px;

                    background: #FFFFFF;

                    border: 1px solid #3A8BFF;

                    border-radius: 18px;

                    margin: 0 auto;

                    text-align: center;

                    line-height: 34px;

                    font-size: 14px;

                    color: #3A8BFF;

                    margin-top: 10px;

                }

            </style>

            <div class="ewmDisable">

                <p>二维码已失效</p>

                <a href="javascript:void(0);" onclick="refrushEwm();">重新获取</a>

            </div>

            <input type="hidden" value="8e6affd0212f40c7b04fd0bafaaba789" id="uuid"/>

            <input type="hidden" value="9d7352c79ec99ace56fd3e34c436ce56" id="enc"/>

            <input type="hidden" value="" id="QRCodeTip"/>

            <img src="/createqr?uuid=8e6affd0212f40c7b04fd0bafaaba789&fid=-1" id="quickCode">

            <span id="ahszk_custom"

                  style="width: 30px;height: 30px;display:inline-block;position: absolute;left: 50%;top: 50%;margin-left: -12px;margin-top: -15px;">

                <img src="/images/fanya/ahszk.png" style="width:30px;height:30px;">

            </span>

        </div>

        <p class="fs14 colorIn tip" id="quickCodeMsg">使用学习通APP扫码登录</p>

    </div>

    

    <div class="social-login" style="display: none" id="thirdLogin">社交账号登录

        <span onclick="to_third_login('weixin')" class="marLeft30" style=" cursor: pointer; "><i><img

                src="/images/common/weixin.png"/></i>微信</span>

        <span onclick="to_third_login('qq')" style=" cursor: pointer; "><i><img

                src="/images/common/qq.png"/></i>QQ</span>

    </div>

    <div class="Rmain-cont" style="display:none" id="step2">

        <div class="g_code_end">

            <img src="" alt="" id="photo"/>

        </div>

        <p class="g_code_name mt30" id="shownickname"></p>

        <p class="g_code_over">请在学习通上点击确认以登录</p>

        <p class="g_code_btn" onclick="changeUser();" style="cursor:pointer;color: #3A8BFF;">返回扫码</p>

    </div>

</div>

<script type="text/javascript">

    /***/

    function to_third_login(type, ref) {

        var fd = $("#fid").val();

        if ("qq" == type) {

            window.location.href = "/oauth2/qq?newversion=true&fid=" + fd + "&refer=" + $("#refer").val();

        } else if ("weixin" == type) {

            window.location.href = "/oauth2/weixin?newversion=true&fid=" + fd + "&refer=" + $("#refer").val();

        }

    }



    function hideQRcode() {

        $(".right").hide();

        $(".default-login-main").attr("class", "main default-login-min");

    }





    function changeUser() {

        window.location.reload();

    }



    var qrInterValObj;

    var enc = $("#enc").val();

    var uuid = $("#uuid").val();

    var ref = decodeURIComponent($("#refer").val());

    var c = 0;



    //超星阅读器 SSREADER/4.1.6.0001 主版本号大于等于4 隐藏扫码登录

    let userAgent = navigator.userAgent;

    if (userAgent.indexOf("SSREADER/") > -1) {

        let versionArr = userAgent.substr(0, 10).split("/");

        if (versionArr[1] >= 4) {

            hideQRcode();

        }

    }



    //定制不显示二维码

    if (1 != 1 || 1 != 1) {

        hideQRcode();

    } else if (-1 == 34415) {

        $("#quickCode").attr("src", "images/guide/34415ewm.png");

        $("#quickCodeMsg").html("扫一扫下载移动客户端");

    } else if (-1 == 9566) {

        $("#quickCode").attr("src", "images/guide/9566ewm.jpg");

        $("#quickCodeMsg").html("扫一扫关注微信公众号");

    } else {

        qrInterValObj = window.setInterval(loopStatus, 3000); //启动计时器，1秒执行一次

        if (-1 == 35014) {

            $("#quickCodeMsg").html("国科大在线APP扫码登录");

            //定制二维码扫码后提示语修改

            $("#step2 p.g_code_over").html("请在移动端上点击确认以登录");

        }

    }

    var QRCodeTip = $("#QRCodeTip").val();

    if (QRCodeTip != "") {

        $("#quickCodeMsg").html(QRCodeTip);

    }



    //重新加载二维码

    function refrushEwm() {

        window.location.reload();

    }



    function loopStatus() {



        jQuery.ajax({

            url: _CP_ + "/getauthstatus/v2",

            type: 'post',

            async: true,

            data: {"enc": enc, "uuid": uuid, "doubleFactorLogin": $('#doubleFactorLogin').val()},

            dataType: 'json',

            success: function (data) {

                if (c >= 50) {

                    window.clearInterval(qrInterValObj);//停止计时器

                    $(".ewmDisable").show();

                    //window.location.reload();

                }

                c++;//记录循环次数

                if (data.status) {

                    window.clearInterval(qrInterValObj);//停止计时器

                    $("#logining").show();

                    if (ref != "undefined" && ref != '') {

                        var url;

                        if (ref == window.location.href) {

                            url = "http://i.mooc.chaoxing.com/space/index";

                        } else {

                            url = ref;

                        }



                        if (data['containTwoFactorLogin']) {

                            url = data['twoFactorLoginPCUrl'] + "&refer=" + encodeURIComponent(url);

                        }



                        if (top.location != self.location) {

                            top.location = url;

                        } else {

                            window.location = url;

                        }

                    } else {

                        var url = "http://i.mooc.chaoxing.com/space/index";



                        if (data['containTwoFactorLogin']) {

                            url = data['twoFactorLoginPCUrl'] + "&_blank=" + _blank + "&refer=" + encodeURIComponent(url);

                        }



                        if (window.parent != this.window) {

                            window.open(url, "_blank");

                        } else {

                            location.href = url;

                        }

                    }

                } else {

                    if (data.type == 4) {//

                        $("#shownickname").html(data.nickname);

                        $("#photo").attr('src', "http://photo.chaoxing.com/p/" + data.uid + "_160");

                        $("#step1").hide();

                        $("#step2").show();

                    } else if (data.type == 6) {//客户端取消

                        location.reload();

                    }

                }

            }



        });

    }



    if ($("#refer").val().indexOf("ahszk.portal.chaoxing.com") != -1) {

        $("#quickCodeMsg").html("使用移动端扫码登录");

        $("#ahszk_custom").show();

    } else {

        $("#ahszk_custom").hide();

    }

    if (navigator.userAgent.indexOf("1000332") != -1 ||

            (ref && ref.indexOf("xidianLoginComplete") > -1)) {

        $("#rightdiv").css("position", "relative");

        $("#rightdiv").css("margin", "0 auto");

        $("#rightdiv").css("border-left", "0 ");

        $("#quickCodeMsg").html("使用学在西电扫码登录");

    }



    if ($("#refer").val().indexOf("dayi100.com") != -1) {

        $("#quickCodeMsg").html("使用大医精诚APP扫码登录");

    }



    function setTab(name, cursel, n) {

        for (i = 1; i <= n; i++) {

            var menu = document.getElementById(name + i);

            var con = document.getElementById("con_" + name + "_" + i);

            menu.className = i == cursel ? "ewmCur" : "";

            con.style.display = i == cursel ? "block" : "none";

        }

    }



    /**

     * 解决windows下font-weight=500时无效果的问题

     */

    if ($('.ewmCur').length > 0) {

        OSnow();

    }



    function OSnow() {

        var agent = navigator.userAgent.toLowerCase();

        var isMac = /macintosh|mac os x/i.test(navigator.userAgent);

        var fontCss = $('.ewmCur');//字体加粗

        //var letterCss = $('.letterSpace');//字间距

        if (agent.indexOf("win32") >= 0 || agent.indexOf("wow32") >= 0) {

            fontCss.css("font-weight", "600")

        }

        if (agent.indexOf("win64") >= 0 || agent.indexOf("wow64") >= 0) {

            fontCss.css("font-weight", "600")

        }

        if (isMac) {

            fontCss.css("font-weight", "500")

        }

    }



</script>

            

            

        </div>

        <!--默认登录 end-->

        <!--通知-->

        <div class="lg-notice" id="notice" style="display:none;">

                            <h2 class="msg-title">通知</h2>

                        <p id="noticeContent">



            </p>

        </div>

    </div>

    <!--登录 end-->

            <div class="filingBox">

            <p>

                <span>Copyright © <t id="cur_year"></t> 北京世纪超星信息技术发展有限责任公司 <a class="col-blue"

                                                                                                href="https://beian.miit.gov.cn">京ICP备10040544号-2</a></span>

                <span class="icpSpan"><i></i><a class="col-blue"

                                                href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=10040544">京公网安备 京ICP备10040544号</a></span>

            </p>

        </div>

    </div>





<script type="text/javascript" src="https://passport2-static.chaoxing.com/js/fanya/login.js?v=20250410"></script>

<script>

    var capInstance = null;//密码滑动校验用

    // let twoLogincapInstance  = null;//双因子登录滑动校验用

    $(function () {

        initTelList();

        let needVcode = $("#needVcode").val();

        if (needVcode == "1") {

            loadSlide(null, function () {

                loginByPhoneAndPwdSubmit();

            });

        }

        try {

            jsBridge.postNotification("CLIENT_PC_CHANGE_WINDOW_SIZE", {

                width: 580,

                height: 650,

            })

        } catch (e) {

        }



    });



    // 备案信息 样式定制

    if ($(".filingBox").length > 0) {

        $(".lg-container .main").addClass("heightCalc")

    } else {

        $(".lg-container .main").removeClass("heightCalc")

    }



    //pc学习通根据appid定制显示

    var pc_ua = navigator.userAgent;

    if (pc_ua.indexOf("1000381") > -1 || pc_ua.indexOf("1000397") > -1) {

        $('.automatic').hide();

        $('.line-bord').hide();

        $('.toRegister_a').removeClass("fr").addClass("blackboard-reg");

        $('#kf').hide();

    }



    var cur_year = new Date().getFullYear();

    $("t#cur_year").text(cur_year);



    function clickAutoLoginBox() {

        $('.auto-login_check_box').toggleClass('checked');

        jsBridge.bind('CLIENT_SET_AUTOLOGIN', function (object) {

            console.log(object);

            if (object.result == 1) {

                var data = eval("(" + object.data + ")");



                jsBridge.unbind('CLIENT_SET_AUTOLOGIN');

            }

        });

        jsBridge.postNotification('CLIENT_SET_AUTOLOGIN', {enable: $('.auto-login_check_box').hasClass('checked')});

    }



</script>

<div id="captcha"></div>

</body>

</html>

