接下来，我们就来分析一下，这个滑块是如何出现的，看看是否可以通过Python代码来通过该滑块验证

首先，我们打开F12进行抓包，然后刷新滑块验证图片

![img](https://i-blog.csdnimg.cn/direct/926c1eb029ae4c81819c12504ffea634.png)

刷新后，一共返回了四个数据包，下面两个可以明显看出来，就是[滑块验证码](https://so.csdn.net/so/search?q=滑块验证码&spm=1001.2101.3001.7020)的背景图和小滑块，那么我们只需要分析一下上面两个包中的返回的内容

![img](https://i-blog.csdnimg.cn/direct/9133b07ef5974a9ba40884799da938f3.png)

conf接口中返回了时间戳与验证码ID

![img](https://i-blog.csdnimg.cn/direct/dc23211ffab6439cad91b0057a997ab2.png)

image接口中返回了token与两个图片url，那么这两个url可能就是滑块验证码的url，所以我们可以验证一下 

![img](https://i-blog.csdnimg.cn/direct/52d51164e91a4ec4969703017d3282c5.png)

OK，没有问题， 意味着我们只需要向这个数据包发送请求，获取到两个图片的url，我们可以先看一下这个数据包的参数有哪些

![img](https://i-blog.csdnimg.cn/direct/9349ee6cf9e9426993c299fbb9e1e3c5.png)

这里就可以发现，密文值不在少数，所以我们需要测试一下，看看有没有值是固定的，重新刷新验证码

 ![img](https://i-blog.csdnimg.cn/direct/e95f7b014f8a47f3a4e31de67652d130.png)

这次就会发现，只会返回三个数据包，并且，captchaid这个参数是固定参数，只有captchaKey、token、iv这三个密文值每次都会变化，所以我们接下来，只需要把这三个密文值给逆向解析出来，然后就可以放心的发送请求了。

当然，开始之前，我们可以先测试一下，看看滑块验证通过与滑块验证失败的区别

![img](https://i-blog.csdnimg.cn/direct/c368bf49269f402d94c2c4fec822870a.png)

当滑块验证失败后，会返回一个result接口，里面的结果为false

 ![img](https://i-blog.csdnimg.cn/direct/7c0cae15f3754c8e8da1c7e9cb9c21d0.png)

当验证通过后，result为true，并且会有extraData值，拿到这个值之后，我们就可以将该值携带进cookie中，实现代码自动登录

![img](https://i-blog.csdnimg.cn/direct/3e9807ebe51445b2bb2b9a243637bc0f.png)

通过查看result接口的参数发现，token值为image这个接口中返回的token，iv则是与image接口中相同的iv，并且滑块滑动的距离是由textClickArr来决定的，OK，那么思路理清楚之后，接下来，我们只需要将image这个接口中的参数逆向解析出来，然后计算出滑块背景与小滑块的距离，最后填入textClickArr，就可以向result接口发送请求，拿到extraData值

思路理清楚后，我们开始实现
![img](https://i-blog.csdnimg.cn/direct/c50352aedcc14046a7fcc1988fd0add3.png)

 通过搜索密文值关键字，直接就能找到加密点

![img](https://i-blog.csdnimg.cn/direct/86a02c70ec49410d8044960fc9b50a11.png)

在这里我们就可以发现所有密文值的赋值操作，但这里是做了js代码的混淆处理，增加了解析难度，但我们仔细观察就可以发现

 ![img](https://i-blog.csdnimg.cn/direct/093a6f4bb2494de5b8e4c4148c38c598.png)

这几个密文值的赋值，其实都是通过同一个函数来实现的，我们可以在这里打个断点，然后再控制台调用这个函数，看看是什么内容

 ![img](https://i-blog.csdnimg.cn/direct/6b2a221756464c43ae99713c82f3ead3.png)

然后我们就会发现， captchaKey的密文值，是有一个时间戳，和一个uuid生成的32位密文值，32位的密文值，最常用的便是md5，所以我们可以验证一下，是否是md5加密

![img](https://i-blog.csdnimg.cn/direct/c985f02fd6f146048d871d0da003fa73.png)

调用该函数，把参数改为12345后，返回了827开头，e7b结尾的密文值，此时，我们再随便找个md5的在线加密网站输入12345

![img](https://i-blog.csdnimg.cn/direct/7142e32863304d15bb222616c8ce80c4.png)

此时我们就会发现，一摸一样，也就是说，所有密文值用的都是标准的md5加密，那我们就更好用代码实现了，只需要搞清楚每个密文值是使用了哪些参数进行加密，即可完成所有密文值逆向解析 

### captchaKey

此密文值我们刚刚知道，是用时间戳与uuid实现，那我们先把这个密文值写出来

`import hashlib
import uuid
import time

times = str(int(time.time() * 1000))  # 生成时间戳

def encrypt_captchaKey():
    captchaKey = hashlib.md5(f'{times}{uuid.uuid4()}'.encode()).hexdigest()  # 拼接参数进行md5加密
    return captchaKey`

此时我们就已经实现了captchaKey的加密，由于uuid和时间戳每次运行都是会发生变化的，所以密文每次运行都不是固定的

### token

![img](https://i-blog.csdnimg.cn/direct/4921cc0d0f5340c1a978aee348dc7242.png)

然后我们再来实现token的密文还原，token中用到了很多参数，我们来一样解析

_0x4e0309：时间戳

_0x3fedba：qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAv      验证码ID，为固定值

_0x589b78：slide

_0x422ded：captchaKey的密文值

0x493e0：300000
token是将前面四个参数用md5加密后，拼接是冒号，然后再拼接上转为整数的时间戳+300000这个整数

def encrypt_token():
    captchaKey = encrypt_captchaKey()
    token = hashlib.md5(f'{times}qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAvslide{captchaKey}'.encode()).hexdigest() + ':' + str(int(times) + 300000)
    return  captchaKey,token

可以将代码中的时间戳和captchaKey改为浏览器中的值，进行模拟加密，看看是否一致

代码加密结果：

![img](https://i-blog.csdnimg.cn/direct/a1a9fcba938443f89079d5555e97badc.png)

 控制台中的加密结果：

![img](https://i-blog.csdnimg.cn/direct/c0fd8cb27e5a4703b5d3e00c152a4fed.png)

OK，token的密文值也已经搞定了，接下来，我们看iv是使用了哪些参数

###  iv

![img](https://i-blog.csdnimg.cn/direct/bc8eb4457a414a1680a7528755326d68.png)

_0x3fedba：qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAv 

_0x589b78：slide

Date[_0x5876dc(0x3d5)]()：时间戳

_0x11dbad()：uuid

 四个参数进行加密

def encrypt_iv():
    iv = hashlib.md5(f'qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAvslide{times}{uuid.uuid4()}'.encode()).hexdigest()
    return iv
AI生成项目
OK，接下来，我们就可以向image接口发送请求，看看是否可以获取到img的url
def get_img_url():
    for i in range(1, 4):
        captchaKey,token = encrypt_token()
        iv = encrypt_iv()
        params['captchaKey'] = captchaKey
        params['token'] = token
        params['iv'] = iv
        response = requests.get(
            'https://captcha.chaoxing.com/captcha/get/verification/image',
            params=params,
            cookies=cookies,
            headers=headers,
        )
        logger.info(response.text)

if __name__ == '__main__':
    get_img_url()

运行截图：

![img](https://i-blog.csdnimg.cn/direct/bc2c350d5adb4e409950f11674c5b8fa.png)

 No problem！！！

然后我们需要处理一下，需要取出响应中的token和两个url，但因为他不是纯json数据，所以我们可以使用正则来取

new_token = re.findall(r'"token":"(.*?)"',response.text)[0]
shadeImage = re.findall(r'"shadeImage":"(.*?)"',response.text)[0]
cutoutImage = re.findall(r'"cutoutImage":"(.*?)"',response.text)[0]
return  new_token,shadeImage,cutoutImage,iv
AI生成项目
因为iv再等会的result接口中也需要用到，所以需要同时返回出去。再向result接口发送请求，之前，最重要的，就是要先用滑块验证码的url去计算出滑动的距离，计算距离最简单的，就是可以使用ddddocr中的接口来实现
import ddddocr

def distance_x():
    for i in range(1,4):
        new_token, shadeImage, cutoutImage, iv = get_img_url()
        slide = ddddocr.DdddOcr(det=False,ocr=False,show_ad=False)
        big_bytes = requests.get(shadeImage,headers=headers).content
        small_bytes = requests.get(cutoutImage,headers=headers).content
        result_x = slide.slide_match(small_bytes,big_bytes,simple_target=True)['target'][0]
        return result_x,new_token,iv

if __name__ == '__main__':

​    distance_x()运行截图：

![img](https://i-blog.csdnimg.cn/direct/fa7d84c646e04f1e9b6c74656ec5adab.png)

成功计算出x的滑动距离，当然，除了通过ddddocr之外，我们还可以通过cv2来实现计算，需要注意的就是cv2需要将图片保存到本地之后进行计算

import cv2

def download_img(content,filename):
    with open(filename, 'wb') as file:
        file.write(content)

def _tran_canny(image):
    # 消除噪声
    image = cv2.GaussianBlur(image, (3, 3), 0)
    return cv2.Canny(image, 50, 150)

def detect_displacement(img_slider_path, image_background_path):
    # # 参数0是灰度模式
    image = cv2.imread(img_slider_path, 0)
    template = cv2.imread(image_background_path, 0)
    # 寻找最佳匹配
    res = cv2.matchTemplate(_tran_canny(image), _tran_canny(template), cv2.TM_CCOEFF_NORMED)
    # 最小值，最大值，并得到最小值, 最大值的索引
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)
    top_left = max_loc[0]  # 横坐标
    # 展示圈出来的区域
    x, y = max_loc  # 获取x,y位置坐标
    w, h = image.shape[::-1]  # 宽高
    cv2.rectangle(template, (x, y), (x + w, y + h), (7, 249, 151), 2)
    return top_left

def cv_distance_x():
    for i in range(1,4):
        new_token, shadeImage, cutoutImage, iv = get_img_url()
        big_img = requests.get(shadeImage,headers=headers).content
        small_img = requests.get(cutoutImage,headers=headers).content
        filename1 = './big.jpg'
        filename2 = './small.jpg'
        download_img(big_img, filename1)
        download_img(small_img, filename2)
        result_x= detect_displacement(filename1, filename2)
        return result_x,new_token,iv

if __name__ == '__main__':
    cv_distance_x()
AI生成项目

运行截图：
![img](https://i-blog.csdnimg.cn/direct/52fef9b3f7734079b18fac8b8f9ff706.png)

两种方式的结果都是只有1~2的距离偏差，所以都是在偏差范围内的，兄弟们可以自己选择一种喜欢的方式去计算距离

接下来，我们只需要拿到x的距离，然后向result接口发送请求

def get_result():
    for i in range(1,4):
        result_x,new_token,iv = distance_x()
        params = {
            'callback': 'cx_captcha_function',
            'captchaId': 'qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAv',
            'type': 'slide',
            'token': f'{new_token}',
            'textClickArr': '[{"x":%s}]'%result_x,
            'coordinate': '[]',
            'runEnv': '10',
            'version': '1.1.20',
            't': 'a',
            'iv': f'{iv}',
            '_': '1750577160309',
        }
        response = requests.get(
            'https://captcha.chaoxing.com/captcha/check/verification/result',
            params=params,
            cookies=cookies,
            headers=headers,
        )
        logger.info(response.text)
AI生成项目

运行截图：

![img](https://i-blog.csdnimg.cn/direct/babf92728c634eefac4375f037b24af8.png)

成功通过滑块验证码，完事！！！ 