"""
超星学习通验证码处理模块
用于处理学习通平台的滑块验证码
可集成到学习通自动化系统中
"""
import requests
import time
import re
import hashlib
import random
import ddddocr
import urllib3
from loguru import logger

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class CaptchaHandler:
    """超星学习通验证码处理类"""

    def __init__(self, session=None, captcha_id=None, referer=None):
        """
        初始化验证码处理器

        Args:
            session: requests.Session对象，如果为None则创建新的会话
            captcha_id: 验证码ID，如果为None则使用默认登录入口ID
            referer: 请求来源页面，如果为None则使用默认登录页面
        """
        self.session = session if session else requests.Session()
        # 禁用SSL验证
        self.session.verify = False
        # 支持动态captcha_id，默认为登录入口ID
        self.captcha_id = captcha_id if captcha_id else 'qDG21VMg9qS5Rcok4cfpnHGnpf5LhcAv'
        # 支持动态referer，默认为登录页面
        default_referer = 'https://v8.chaoxing.com/'
        self.referer = referer if referer else default_referer
        self.headers = {
            'referer': self.referer,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36'
        }
        # 初始化验证码识别器
        try:
            self.detector = ddddocr.DdddOcr(det=False, ocr=True)
            logger.info("验证码识别器初始化成功")
        except Exception as e:
            logger.error(f"验证码识别器初始化失败: {str(e)}")
            self.detector = None
    
    def _generate_random_id(self):
        """
        生成随机UUID格式的ID
        模拟JS中的随机ID生成函数
        
        Returns:
            str: 随机生成的UUID格式ID
        """
        chars = "0123456789abcdef"
        result = [''] * 36
        for i in range(36):
            if i in [8, 13, 18, 23]:
                result[i] = '-'
            elif i == 14:
                result[i] = '4'
            elif i == 19:
                result[i] = chars[random.randint(0, 7) | 8]
            else:
                result[i] = chars[random.randint(0, 15)]
        return ''.join(result)
    
    def _get_sign_params(self, timestamp=None):
        """
        获取验证码请求所需的签名参数
        
        Args:
            timestamp: 时间戳，如果为None则使用当前时间
            
        Returns:
            dict: 包含captchaKey, token, IV的字典
        """
        if timestamp is None:
            timestamp = int(time.time() * 1000)
            
        random_id = self._generate_random_id()
        
        # 生成captchaKey
        captcha_key = hashlib.md5(f"{timestamp}{random_id}".encode()).hexdigest()
        
        # 生成token
        token_str = f"{timestamp}{self.captcha_id}slide{captcha_key}"
        token = f"{hashlib.md5(token_str.encode()).hexdigest()}:{int(timestamp) + 300000}"
        
        # 生成IV
        iv = hashlib.md5(f"{self.captcha_id}slide{int(time.time() * 1000)}{self._generate_random_id()}".encode()).hexdigest()
        
        return {
            'captchaKey': captcha_key,
            'token': token,
            'IV': iv,
            'timestamp': timestamp
        }
    
    def get_captcha_images(self):
        """
        获取验证码图片
        
        Returns:
            tuple: (背景图片二进制数据, 滑块图片二进制数据, token) 或 (None, None, None)
        """
        try:
            # 请求网址
            url = 'https://captcha.chaoxing.com/captcha/get/verification/image'
            
            # 获取签名参数
            sign_data = self._get_sign_params()
            
            # 查询参数
            params = {
                'callback': 'cx_captcha_function',
                'captchaId': self.captcha_id,
                'type': 'slide',
                'version': '1.1.20',
                'captchaKey': sign_data['captchaKey'],
                'token': sign_data['token'],
                'referer': self.referer,  # 使用动态referer而不是硬编码
                'iv': sign_data['IV'],
                '_': sign_data['timestamp'],
            }
            
            # 发送请求获取验证码图片
            response = self.session.get(url=url, params=params, headers=self.headers, timeout=10, verify=False)
            
            if response.status_code != 200:
                logger.error(f"获取验证码失败，状态码: {response.status_code}")
                return None, None, None
                
            # 提取验证图片 shadeImage(缺口图) cutoutImage(滑块图)
            match = re.findall('"shadeImage":"(.*?)","cutoutImage":"(.*?)"', response.text)
            if not match:
                logger.error("未找到验证码图片URL")
                return None, None, None
                
            shade_image_url, cutout_image_url = match[0]
            
            # 提取token
            token_match = re.findall('"token":"(.*?)"', response.text)
            if not token_match:
                logger.error("未找到token")
                return None, None, None
                
            token = token_match[0]
            
            # 获取缺口图片内容
            shade_image_content = self.session.get(url=shade_image_url, headers=self.headers, timeout=10, verify=False).content

            # 获取滑块图片内容
            cutout_image_content = self.session.get(url=cutout_image_url, headers=self.headers, timeout=10, verify=False).content
            
            return shade_image_content, cutout_image_content, token
            
        except Exception as e:
            logger.error(f"获取验证码图片异常: {str(e)}")
            return None, None, None
    
    def recognize_slide_distance(self, shade_image_content, cutout_image_content):
        """
        识别滑块验证码的滑动距离
        
        Args:
            shade_image_content: 背景图片二进制数据
            cutout_image_content: 滑块图片二进制数据
            
        Returns:
            int: 滑动距离，失败返回None
        """
        try:
            if self.detector is None:
                logger.error("验证码识别器未初始化")
                return None
                
            # 保存原始图片用于调试（可选）
            try:
                with open('debug_shade.jpg', 'wb') as f:
                    f.write(shade_image_content)
                with open('debug_cutout.jpg', 'wb') as f:
                    f.write(cutout_image_content)
                logger.debug("已保存调试图片")
            except:
                pass

            # 多次识别取最佳结果（提高精准度）
            recognition_results = []
            for attempt in range(3):
                try:
                    # 验证图片识别（使用52pojie文章推荐的参数）
                    result = self.detector.slide_match(shade_image_content, cutout_image_content, simple_target=True)
                    x_distance = result['target'][0]
                    recognition_results.append(x_distance)
                    logger.debug(f"第{attempt+1}次识别结果: x = {x_distance}")
                except Exception as e:
                    logger.warning(f"第{attempt+1}次识别失败: {e}")
                    continue

            if not recognition_results:
                logger.error("所有识别尝试都失败")
                return None

            # 计算平均值并进行智能筛选
            if len(recognition_results) >= 2:
                # 去除异常值（与平均值差距过大的值）
                avg = sum(recognition_results) / len(recognition_results)
                filtered_results = [x for x in recognition_results if abs(x - avg) <= 20]
                if filtered_results:
                    recognition_results = filtered_results

            # 使用最终结果
            final_x = sum(recognition_results) / len(recognition_results)
            logger.info(f"识别滑块位置成功: 原始结果={recognition_results}, 最终位置={final_x:.1f}")

            # 基于52pojie文章和实际测试的微调策略
            # 根据经验，ddddocr的结果可能需要小幅调整
            import random

            # 添加小幅随机微调（-2到+2像素），模拟真实用户的不精确性
            random_offset = random.uniform(-2, 2)
            adjusted_x = final_x + random_offset

            # 合理性检查：验证识别结果是否在正常范围内
            if adjusted_x < 0:
                adjusted_x = max(0, final_x)
            elif adjusted_x > 400:
                adjusted_x = min(400, final_x)

            logger.info(f"最终滑块位置: x = {adjusted_x:.1f} (微调偏移: {random_offset:.1f})")
            return int(adjusted_x)
            
        except Exception as e:
            logger.error(f"识别滑块位置异常: {str(e)}")
            return None
    
    def verify_captcha(self, token, x_distance):
        """
        提交验证码验证

        Args:
            token: 验证码token
            x_distance: 滑动距离

        Returns:
            tuple: (验证成功状态, validate字符串) - (bool, str)
        """
        try:
            # 验证链接
            url = 'https://captcha.chaoxing.com/captcha/check/verification/result'
            current_time = int(time.time() * 1000)

            # 请求参数
            params = {
                'callback': 'cx_captcha_function',
                'captchaId': self.captcha_id,
                'type': 'slide',
                'token': token,
                'textClickArr': f'[{{"x":{x_distance}}}]',
                'coordinate': '[]',
                'runEnv': '10',
                'version': '1.1.20',
                't': 'a',
                'iv': 'dcac328825997752b5099050a3b4c9ea',
                '_': current_time,
            }

            # 发送验证请求
            response = self.session.get(url=url, params=params, headers=self.headers, timeout=10, verify=False)

            if response.status_code != 200:
                logger.error(f"验证码验证请求失败，状态码: {response.status_code}")
                return False, None

            # 解析JSONP响应
            response_text = response.text.strip()
            logger.info(f"验证码响应: {response_text}")

            # 提取JSON部分 (去掉 cx_captcha_function( 和 ))
            if response_text.startswith('cx_captcha_function(') and response_text.endswith(')'):
                json_str = response_text[20:-1]  # 去掉前缀和后缀

                try:
                    import json
                    result_data = json.loads(json_str)

                    # 检查验证是否成功
                    if result_data.get('result') is True or result_data.get('error') == 0:
                        # 提取validate字符串
                        validate_str = None
                        extra_data = result_data.get('extraData')
                        if extra_data:
                            try:
                                extra_json = json.loads(extra_data)
                                validate_str = extra_json.get('validate')
                            except:
                                # 如果extraData不是JSON，尝试正则提取
                                import re
                                validate_match = re.search(r'"validate":"([^"]+)"', extra_data)
                                if validate_match:
                                    validate_str = validate_match.group(1)

                        logger.success(f"验证码验证成功，validate: {validate_str}")
                        return True, validate_str
                    else:
                        logger.warning(f"验证码验证失败: result={result_data.get('result')}, error={result_data.get('error')}")
                        return False, None

                except json.JSONDecodeError as e:
                    logger.error(f"解析验证码响应JSON失败: {e}")
                    return False, None
            else:
                # 兼容旧的响应格式
                if "success" in response_text and "true" in response_text:
                    logger.success("验证码验证成功（旧格式）")
                    return True, None
                else:
                    logger.warning(f"验证码验证失败: {response_text}")
                    return False, None

        except Exception as e:
            logger.error(f"验证码验证异常: {str(e)}")
            return False, None
    
    def solve_captcha(self, max_attempts=3):
        """
        完整的验证码解决方案，自动获取、识别和验证

        Args:
            max_attempts: 最大尝试次数

        Returns:
            tuple: (验证成功状态, validate字符串) - (bool, str)
        """
        for attempt in range(1, max_attempts + 1):
            try:
                logger.info(f"第{attempt}次尝试解决验证码...")

                # 获取验证码图片
                shade_image, cutout_image, token = self.get_captcha_images()
                if shade_image is None or cutout_image is None or token is None:
                    logger.error("获取验证码图片失败")
                    continue

                # 识别滑动距离
                x_distance = self.recognize_slide_distance(shade_image, cutout_image)
                if x_distance is None:
                    logger.error("识别滑动距离失败")
                    continue

                # 验证验证码
                success, validate_str = self.verify_captcha(token, x_distance)
                if success:
                    return True, validate_str

                # 验证失败，等待一段时间再重试
                time.sleep(2)

            except Exception as e:
                logger.error(f"解决验证码异常: {str(e)}")
                time.sleep(2)

        logger.error(f"验证码解决失败，已尝试{max_attempts}次")
        return False, None

# 测试代码
if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add("captcha.log", rotation="10 MB", level="INFO")
    logger.add(lambda msg: print(msg), level="INFO")

    # 测试验证码处理
    captcha_handler = CaptchaHandler()
    success, validate_str = captcha_handler.solve_captcha()
    print(f"验证码处理结果: {'成功' if success else '失败'}")
    if success and validate_str:
        print(f"获取到validate字符串: {validate_str}")