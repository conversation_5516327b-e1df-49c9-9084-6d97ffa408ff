#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试考试滑块验证码处理脚本
用于验证ExamCaptchaHandler的功能
"""

import sys
import os
import time
from ExamCaptchaHandler import ExamCaptchaHandler

def test_exam_captcha_handler():
    """
    测试考试验证码处理器
    """
    print("=== 测试考试滑块验证码处理器 ===")
    
    # 考试入口URL
    exam_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?reset=true&examId=7689933&courseId=253891757&classId=124398558&cpi=404722142"
    
    try:
        # 创建考试验证码处理器
        print("创建考试验证码处理器...")
        exam_captcha_handler = ExamCaptchaHandler(exam_url=exam_url)
        
        print(f"使用的captchaId: {exam_captcha_handler.captcha_id}")
        print(f"使用的referer: {exam_captcha_handler.referer}")
        
        # 测试获取验证码图片
        print("\n测试获取验证码图片...")
        shade_image, cutout_image, token = exam_captcha_handler.get_captcha_images()
        
        if shade_image and cutout_image and token:
            print("✅ 成功获取验证码图片")
            print(f"背景图片大小: {len(shade_image)} bytes")
            print(f"滑块图片大小: {len(cutout_image)} bytes")
            print(f"Token: {token[:50]}...")
            
            # 测试识别滑动距离
            print("\n测试识别滑动距离...")
            x_distance = exam_captcha_handler.recognize_slide_distance(shade_image, cutout_image)
            
            if x_distance is not None:
                print(f"✅ 成功识别滑动距离: {x_distance}")
                
                # 测试验证验证码
                print("\n测试验证验证码...")
                success, validate_str = exam_captcha_handler.verify_captcha(token, x_distance)
                
                if success:
                    print("✅ 验证码验证成功！")
                    if validate_str:
                        print(f"获取到validate字符串: {validate_str}")
                else:
                    print("❌ 验证码验证失败")
            else:
                print("❌ 识别滑动距离失败")
        else:
            print("❌ 获取验证码图片失败")
        
        # 测试完整的验证码解决方案
        print("\n测试完整的验证码解决方案...")
        success, validate_str = exam_captcha_handler.solve_exam_captcha(max_attempts=3)
        
        if success:
            print("🎉 考试验证码完整解决方案测试成功！")
            if validate_str:
                print(f"最终获取到validate字符串: {validate_str}")
        else:
            print("❌ 考试验证码完整解决方案测试失败")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_captcha_id_detection():
    """
    测试captchaId检测功能
    """
    print("\n=== 测试captchaId检测功能 ===")
    
    # 模拟页面内容
    test_page_content = '''
    <html>
    <script>
        var captchaId = "Ew0z9skxsLzVKQjmeObQiRVLxkxbPkRF";
        var config = {
            "captcha_id": "TestCaptchaId123",
            "type": "slide"
        };
    </script>
    </html>
    '''
    
    try:
        exam_captcha_handler = ExamCaptchaHandler()
        detected_id = exam_captcha_handler.detect_captcha_id_from_page(test_page_content)
        print(f"检测到的captchaId: {detected_id}")
        
        # 测试更新captchaId
        exam_captcha_handler.update_captcha_id(detected_id)
        print(f"更新后的captchaId: {exam_captcha_handler.captcha_id}")
        
    except Exception as e:
        print(f"测试captchaId检测时出错: {e}")

def compare_login_vs_exam_captcha():
    """
    对比登录入口和考试入口的验证码处理
    """
    print("\n=== 对比登录入口和考试入口的验证码处理 ===")
    
    try:
        # 登录入口验证码处理器
        from CaptchaHandler import CaptchaHandler
        login_captcha_handler = CaptchaHandler()
        print(f"登录入口 - captchaId: {login_captcha_handler.captcha_id}")
        print(f"登录入口 - referer: {login_captcha_handler.referer}")
        
        # 考试入口验证码处理器
        exam_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes?reset=true&examId=7689933&courseId=253891757&classId=124398558&cpi=404722142"
        exam_captcha_handler = ExamCaptchaHandler(exam_url=exam_url)
        print(f"考试入口 - captchaId: {exam_captcha_handler.captcha_id}")
        print(f"考试入口 - referer: {exam_captcha_handler.referer}")
        
        print("\n差异分析:")
        print(f"captchaId是否相同: {login_captcha_handler.captcha_id == exam_captcha_handler.captcha_id}")
        print(f"referer是否相同: {login_captcha_handler.referer == exam_captcha_handler.referer}")
        
    except Exception as e:
        print(f"对比测试时出错: {e}")

def main():
    """
    主测试函数
    """
    print("开始测试考试滑块验证码处理功能...\n")
    
    # 测试1: 基本功能测试
    test_exam_captcha_handler()
    
    # 测试2: captchaId检测功能
    test_captcha_id_detection()
    
    # 测试3: 对比登录入口和考试入口
    compare_login_vs_exam_captcha()
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
